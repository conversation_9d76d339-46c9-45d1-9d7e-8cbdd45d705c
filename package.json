{"name": "motivio", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "start:prod": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:generate": "drizzle-kit generate", "db:setup": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat setup' : 'scripts/db-setup.sh setup', {stdio: 'inherit'})\"", "db:setup:dev": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat setup dev pgadmin' : 'scripts/db-setup.sh setup dev pgadmin', {stdio: 'inherit'})\"", "db:start": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat start' : 'scripts/db-setup.sh start', {stdio: 'inherit'})\"", "db:start:dev": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat start dev pgadmin' : 'scripts/db-setup.sh start dev pgadmin', {stdio: 'inherit'})\"", "db:stop": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat stop' : 'scripts/db-setup.sh stop', {stdio: 'inherit'})\"", "db:stop:dev": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat stop dev' : 'scripts/db-setup.sh stop dev', {stdio: 'inherit'})\"", "db:shell": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat shell' : 'scripts/db-setup.sh shell', {stdio: 'inherit'})\"", "db:shell:dev": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat shell dev' : 'scripts/db-setup.sh shell dev', {stdio: 'inherit'})\"", "db:reset": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat reset' : 'scripts/db-setup.sh reset', {stdio: 'inherit'})\"", "db:reset:dev": "node -e \"require('child_process').execSync(process.platform === 'win32' ? 'scripts\\\\db-setup.bat reset dev' : 'scripts/db-setup.sh reset dev', {stdio: 'inherit'})\"", "restart": "node restart-dev.js", "restart:clean": "node restart-dev.js", "build:prod": "NODE_ENV=production vite build && NODE_ENV=production esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist --minify --target=es2020", "build:railway": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist --target=es2020", "build:simple": "vite build", "preview": "npm run build && npm run start:prod", "test:prod": "node test-production.js", "deploy": "./deploy.sh"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tanstack/react-query": "^5.60.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "compression": "^1.7.4", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "helmet": "^7.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.1.3", "@types/compression": "^1.7.5", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.17", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}